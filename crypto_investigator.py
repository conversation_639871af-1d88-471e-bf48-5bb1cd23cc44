"""
Bitcoin Forensic Investigation Tool v2.0

A professional-grade cryptocurrency forensics tool for tracing Bitcoin transactions
and analyzing fund flows. This tool provides comprehensive investigation capabilities
with proper error handling, logging, and evidence collection features.

Author: Cryptocurrency Investigation Team
License: MIT
Version: 2.0.0
"""

import requests
import networkx as nx
import plotly.graph_objects as go
from collections import deque
import time
import logging
from typing import Dict, List, Optional, Tuple, Set, Any
import json
from datetime import datetime
import re
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import uuid
import sys
import argparse
from tqdm import tqdm

# Configure logging
def setup_logging(log_level: str = "INFO", log_file: str = "crypto_investigation.log") -> logging.Logger:
    """Set up logging configuration."""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)
    log_path = Path("logs") / log_file

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

@dataclass
class InvestigationConfig:
    """Configuration class for investigation parameters."""
    api_base_url: str = "https://blockstream.info/api/"
    rate_limit_delay: float = 0.1  # Delay between API calls in seconds
    max_retries: int = 3
    request_timeout: int = 30
    max_depth: int = 5
    save_reports: bool = True
    save_visualizations: bool = True
    output_directory: str = "investigation_results"

@dataclass
class TransactionInfo:
    """Data class for transaction information."""
    txid: str
    from_address: str
    to_address: str
    amount_btc: float
    depth: int
    timestamp: str
    block_height: Optional[int] = None
    confirmations: bool = False
    investigation_id: str = ""

@dataclass
class AuditLogEntry:
    """Data class for audit log entries."""
    timestamp: str
    investigation_id: str
    action: str
    details: Dict[str, Any]
    user_id: str = "system"
    ip_address: str = "localhost"

@dataclass
class EvidenceItem:
    """Data class for evidence items."""
    evidence_id: str
    investigation_id: str
    evidence_type: str  # "transaction", "address", "pattern", "analysis"
    description: str
    data: Dict[str, Any]
    timestamp: str
    hash_value: str
    chain_of_custody: List[Dict[str, Any]]

class BitcoinForensicsInvestigator:
    """
    Professional Bitcoin forensics investigation tool.

    This class provides comprehensive functionality for tracing Bitcoin transactions,
    analyzing fund flows, and generating detailed forensic reports.
    """

    def __init__(self, config: Optional[InvestigationConfig] = None):
        """Initialize the investigator with configuration."""
        self.config = config or InvestigationConfig()
        self.investigation_id = str(uuid.uuid4())
        self.session = requests.Session()

        # Evidence and audit tracking
        self.audit_log: List[AuditLogEntry] = []
        self.evidence_items: List[EvidenceItem] = []

        # Create output directory
        Path(self.config.output_directory).mkdir(exist_ok=True)

        # Log initialization
        self._log_audit_event("investigation_initialized", {
            "investigation_id": self.investigation_id,
            "config": asdict(self.config)
        })

        logger.info(f"Initialized Bitcoin Forensics Investigator (ID: {self.investigation_id})")

    def validate_bitcoin_address(self, address: str) -> bool:
        """Validate Bitcoin address format."""
        if not address:
            return False

        # Basic validation for Bitcoin addresses
        if len(address) < 26 or len(address) > 62:
            return False

        # Check for valid Bitcoin address patterns
        patterns = [
            r'^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$',  # Legacy P2PKH/P2SH
            r'^bc1[a-z0-9]{39,59}$',               # Bech32 P2WPKH/P2WSH
            r'^bc1p[a-z0-9]{58}$'                  # Bech32m P2TR
        ]

        return any(re.match(pattern, address) for pattern in patterns)

    def validate_transaction_id(self, txid: str) -> bool:
        """Validate Bitcoin transaction ID format."""
        if not txid or len(txid) != 64:
            return False
        return re.match(r'^[a-fA-F0-9]{64}$', txid) is not None

    def get_transaction_data(self, txid: str) -> Optional[Dict]:
        """Fetches detailed transaction data from the Blockstream API with retry logic."""
        if not self.validate_transaction_id(txid):
            logger.error(f"Invalid transaction ID format: {txid}")
            return None

        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.rate_limit_delay)  # Rate limiting
                response = self.session.get(
                    f"{self.config.api_base_url}tx/{txid}",
                    timeout=self.config.request_timeout
                )
                response.raise_for_status()

                data = response.json()
                logger.info(f"Successfully fetched transaction data for {txid}")
                return data

            except requests.exceptions.Timeout:
                logger.warning(f"Timeout fetching transaction {txid}, attempt {attempt + 1}/{self.config.max_retries}")
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    logger.error(f"Transaction not found: {txid}")
                    return None
                elif e.response.status_code == 429:
                    logger.warning(f"Rate limited, waiting before retry...")
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"HTTP error fetching transaction {txid}: {e}")
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error fetching transaction {txid}: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error for transaction {txid}: {e}")

            if attempt < self.config.max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to fetch transaction data after {self.config.max_retries} attempts: {txid}")
        return None

    def get_outspend_data(self, txid: str, vout_index: int) -> Optional[Dict]:
        """Fetches outspend data for a specific transaction output."""
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.rate_limit_delay)
                response = self.session.get(
                    f"{self.config.api_base_url}tx/{txid}/outspend/{vout_index}",
                    timeout=self.config.request_timeout
                )
                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                logger.warning(f"Error fetching outspend data for {txid}:{vout_index}, attempt {attempt + 1}: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(2 ** attempt)

        return None

    def _log_audit_event(self, action: str, details: Dict[str, Any], user_id: str = "system") -> None:
        """Log an audit event for chain of custody."""
        audit_entry = AuditLogEntry(
            timestamp=datetime.now().isoformat(),
            investigation_id=self.investigation_id,
            action=action,
            details=details,
            user_id=user_id,
            ip_address="localhost"  # In a real system, this would be the actual IP
        )
        self.audit_log.append(audit_entry)
        logger.info(f"Audit event logged: {action}")

    def _create_evidence_item(self, evidence_type: str, description: str, data: Dict[str, Any]) -> EvidenceItem:
        """Create a new evidence item with proper chain of custody."""
        evidence_id = str(uuid.uuid4())

        # Create hash of the evidence data for integrity
        evidence_hash = hashlib.sha256(
            json.dumps(data, sort_keys=True, default=str).encode()
        ).hexdigest()

        # Initialize chain of custody
        chain_of_custody = [{
            "timestamp": datetime.now().isoformat(),
            "action": "evidence_created",
            "user_id": "system",
            "description": f"Evidence item created: {description}"
        }]

        evidence_item = EvidenceItem(
            evidence_id=evidence_id,
            investigation_id=self.investigation_id,
            evidence_type=evidence_type,
            description=description,
            data=data,
            timestamp=datetime.now().isoformat(),
            hash_value=evidence_hash,
            chain_of_custody=chain_of_custody
        )

        self.evidence_items.append(evidence_item)

        # Log audit event
        self._log_audit_event("evidence_created", {
            "evidence_id": evidence_id,
            "evidence_type": evidence_type,
            "description": description,
            "hash": evidence_hash
        })

        return evidence_item

    def _update_evidence_chain_of_custody(self, evidence_id: str, action: str, description: str, user_id: str = "system") -> None:
        """Update the chain of custody for an evidence item."""
        for evidence in self.evidence_items:
            if evidence.evidence_id == evidence_id:
                custody_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "action": action,
                    "user_id": user_id,
                    "description": description
                }
                evidence.chain_of_custody.append(custody_entry)

                # Log audit event
                self._log_audit_event("evidence_custody_updated", {
                    "evidence_id": evidence_id,
                    "action": action,
                    "description": description
                })
                break

    def generate_audit_trail_report(self) -> Dict[str, Any]:
        """Generate a comprehensive audit trail report."""
        return {
            "investigation_id": self.investigation_id,
            "report_timestamp": datetime.now().isoformat(),
            "total_audit_events": len(self.audit_log),
            "total_evidence_items": len(self.evidence_items),
            "audit_events": [asdict(entry) for entry in self.audit_log],
            "evidence_summary": [
                {
                    "evidence_id": item.evidence_id,
                    "type": item.evidence_type,
                    "description": item.description,
                    "timestamp": item.timestamp,
                    "hash": item.hash_value,
                    "custody_events": len(item.chain_of_custody)
                }
                for item in self.evidence_items
            ]
        }

    def export_evidence_package(self, output_format: str = "json") -> str:
        """Export complete evidence package for legal proceedings."""
        evidence_package = {
            "investigation_metadata": {
                "investigation_id": self.investigation_id,
                "export_timestamp": datetime.now().isoformat(),
                "investigator_version": "2.0.0",
                "export_format": output_format
            },
            "audit_trail": self.generate_audit_trail_report(),
            "evidence_items": [asdict(item) for item in self.evidence_items],
            "integrity_verification": {
                "total_evidence_items": len(self.evidence_items),
                "package_hash": self._calculate_package_hash()
            }
        }

        # Save evidence package
        output_path = Path(self.config.output_directory)
        if output_format.lower() == "json":
            filename = output_path / f"evidence_package_{self.investigation_id}.json"
            with open(filename, 'w') as f:
                json.dump(evidence_package, f, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {output_format}")

        # Log the export
        self._log_audit_event("evidence_package_exported", {
            "filename": str(filename),
            "format": output_format,
            "items_count": len(self.evidence_items)
        })

        logger.info(f"Evidence package exported to {filename}")
        return str(filename)

    def _calculate_package_hash(self) -> str:
        """Calculate hash of the entire evidence package for integrity verification."""
        package_data = {
            "investigation_id": self.investigation_id,
            "evidence_items": [item.hash_value for item in self.evidence_items],
            "audit_events": len(self.audit_log)
        }
        return hashlib.sha256(
            json.dumps(package_data, sort_keys=True).encode()
        ).hexdigest()

    def analyze_transaction_patterns(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze transaction patterns for suspicious activity detection."""
        if not report:
            return {}

        patterns = {
            "timing_analysis": self._analyze_timing_patterns(report),
            "amount_analysis": self._analyze_amount_patterns(report),
            "address_reuse": self._analyze_address_reuse(report),
            "clustering_hints": self._analyze_clustering_hints(report),
            "risk_indicators": self._calculate_risk_indicators(report)
        }

        logger.info(f"Pattern analysis completed for {len(report)} transactions")
        return patterns

    def _analyze_timing_patterns(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze timing patterns in transactions."""
        if len(report) < 2:
            return {"status": "insufficient_data"}

        # Convert timestamps to datetime objects for analysis
        timestamps = []
        for tx in report:
            try:
                timestamps.append(datetime.fromisoformat(tx.timestamp.replace('Z', '+00:00')))
            except:
                continue

        if len(timestamps) < 2:
            return {"status": "invalid_timestamps"}

        # Calculate time intervals
        intervals = []
        for i in range(1, len(timestamps)):
            interval = (timestamps[i] - timestamps[i-1]).total_seconds()
            intervals.append(interval)

        avg_interval = sum(intervals) / len(intervals) if intervals else 0

        # Detect rapid succession (potential automated behavior)
        rapid_succession = sum(1 for interval in intervals if interval < 300)  # < 5 minutes

        return {
            "average_interval_seconds": avg_interval,
            "rapid_succession_count": rapid_succession,
            "total_intervals": len(intervals),
            "suspicious_timing": rapid_succession > len(intervals) * 0.5
        }

    def _analyze_amount_patterns(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze amount patterns for suspicious activity."""
        amounts = [tx.amount_btc for tx in report]

        if not amounts:
            return {"status": "no_amounts"}

        # Statistical analysis
        total_amount = sum(amounts)
        avg_amount = total_amount / len(amounts)
        max_amount = max(amounts)
        min_amount = min(amounts)

        # Detect round numbers (potential structuring)
        round_amounts = sum(1 for amount in amounts if amount == round(amount, 8))

        # Detect similar amounts (potential splitting)
        similar_threshold = 0.001  # 0.001 BTC threshold
        similar_amounts = 0
        for i, amount1 in enumerate(amounts):
            for amount2 in amounts[i+1:]:
                if abs(amount1 - amount2) < similar_threshold:
                    similar_amounts += 1

        return {
            "total_amount": total_amount,
            "average_amount": avg_amount,
            "max_amount": max_amount,
            "min_amount": min_amount,
            "round_amounts_count": round_amounts,
            "similar_amounts_count": similar_amounts,
            "potential_structuring": round_amounts > len(amounts) * 0.3,
            "potential_splitting": similar_amounts > 0
        }

    def _analyze_address_reuse(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze address reuse patterns."""
        from_addresses = [tx.from_address for tx in report]
        to_addresses = [tx.to_address for tx in report]

        from_counts = {}
        to_counts = {}

        for addr in from_addresses:
            from_counts[addr] = from_counts.get(addr, 0) + 1

        for addr in to_addresses:
            to_counts[addr] = to_counts.get(addr, 0) + 1

        # Find addresses used multiple times
        reused_from = {addr: count for addr, count in from_counts.items() if count > 1}
        reused_to = {addr: count for addr, count in to_counts.items() if count > 1}

        return {
            "unique_from_addresses": len(from_counts),
            "unique_to_addresses": len(to_counts),
            "reused_from_addresses": reused_from,
            "reused_to_addresses": reused_to,
            "address_reuse_detected": len(reused_from) > 0 or len(reused_to) > 0
        }

    def _analyze_clustering_hints(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze potential wallet clustering hints."""
        # Group transactions by common patterns
        clusters = {}

        for tx in report:
            # Simple clustering based on timing and amounts
            cluster_key = f"{tx.depth}_{round(tx.amount_btc, 4)}"
            if cluster_key not in clusters:
                clusters[cluster_key] = []
            clusters[cluster_key].append(tx)

        # Find potential clusters (multiple transactions with similar patterns)
        potential_clusters = {k: v for k, v in clusters.items() if len(v) > 1}

        return {
            "total_clusters": len(clusters),
            "potential_clusters": len(potential_clusters),
            "cluster_details": {k: len(v) for k, v in potential_clusters.items()},
            "clustering_detected": len(potential_clusters) > 0
        }

    def _calculate_risk_indicators(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Calculate risk indicators based on transaction patterns."""
        risk_score = 0
        risk_factors = []

        # High transaction volume
        if len(report) > 10:
            risk_score += 2
            risk_factors.append("high_transaction_volume")

        # Large amounts
        total_amount = sum(tx.amount_btc for tx in report)
        if total_amount > 10:  # > 10 BTC
            risk_score += 3
            risk_factors.append("large_amounts")

        # Deep transaction chains
        max_depth = max(tx.depth for tx in report) if report else 0
        if max_depth > 3:
            risk_score += 2
            risk_factors.append("deep_transaction_chains")

        # Rapid transactions
        timing_analysis = self._analyze_timing_patterns(report)
        if timing_analysis.get("suspicious_timing", False):
            risk_score += 3
            risk_factors.append("suspicious_timing")

        # Amount patterns
        amount_analysis = self._analyze_amount_patterns(report)
        if amount_analysis.get("potential_structuring", False):
            risk_score += 2
            risk_factors.append("potential_structuring")

        # Risk level classification
        if risk_score >= 8:
            risk_level = "HIGH"
        elif risk_score >= 5:
            risk_level = "MEDIUM"
        elif risk_score >= 2:
            risk_level = "LOW"
        else:
            risk_level = "MINIMAL"

        return {
            "risk_score": risk_score,
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "total_amount": total_amount,
            "transaction_count": len(report),
            "max_depth": max_depth
        }

    def detect_suspicious_activity(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect various types of suspicious activity patterns."""
        suspicious_activities = {
            "mixing_services": self._detect_mixing_services(report),
            "exchange_deposits": self._detect_exchange_deposits(report),
            "peel_chains": self._detect_peel_chains(report),
            "consolidation_patterns": self._detect_consolidation_patterns(report),
            "privacy_coins": self._detect_privacy_coin_interactions(report)
        }

        # Overall suspicion score
        suspicion_score = 0
        for activity, details in suspicious_activities.items():
            if details.get("detected", False):
                suspicion_score += details.get("severity", 1)

        suspicious_activities["overall_suspicion_score"] = suspicion_score
        suspicious_activities["suspicion_level"] = self._classify_suspicion_level(suspicion_score)

        return suspicious_activities

    def _detect_mixing_services(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect potential mixing service usage."""
        # Known mixing service patterns (simplified)
        mixing_indicators = 0

        # Look for multiple small outputs (typical mixing pattern)
        small_outputs = sum(1 for tx in report if tx.amount_btc < 0.01)
        if small_outputs > len(report) * 0.6:
            mixing_indicators += 1

        # Look for round amounts (mixing services often use round amounts)
        round_amounts = sum(1 for tx in report if tx.amount_btc == round(tx.amount_btc, 8))
        if round_amounts > len(report) * 0.4:
            mixing_indicators += 1

        return {
            "detected": mixing_indicators >= 1,
            "indicators": mixing_indicators,
            "severity": 3,
            "details": {
                "small_outputs": small_outputs,
                "round_amounts": round_amounts
            }
        }

    def _detect_exchange_deposits(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect potential exchange deposit patterns."""
        # Look for addresses that receive from multiple sources (exchange-like behavior)
        to_address_sources = {}
        for tx in report:
            if tx.to_address not in to_address_sources:
                to_address_sources[tx.to_address] = set()
            to_address_sources[tx.to_address].add(tx.from_address)

        # Find addresses receiving from multiple sources
        potential_exchanges = {
            addr: len(sources) for addr, sources in to_address_sources.items()
            if len(sources) > 2
        }

        return {
            "detected": len(potential_exchanges) > 0,
            "potential_exchanges": potential_exchanges,
            "severity": 2,
            "details": {
                "addresses_with_multiple_sources": len(potential_exchanges)
            }
        }

    def _detect_peel_chains(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect peel chain patterns (gradual fund extraction)."""
        if len(report) < 3:
            return {"detected": False, "severity": 2}

        # Sort by depth to analyze chain progression
        sorted_txs = sorted(report, key=lambda x: x.depth)

        # Look for decreasing amounts pattern
        decreasing_pattern = 0
        for i in range(1, len(sorted_txs)):
            if sorted_txs[i].amount_btc < sorted_txs[i-1].amount_btc:
                decreasing_pattern += 1

        # Peel chain detected if most transactions show decreasing amounts
        is_peel_chain = decreasing_pattern > len(sorted_txs) * 0.6

        return {
            "detected": is_peel_chain,
            "decreasing_transactions": decreasing_pattern,
            "total_transactions": len(sorted_txs),
            "severity": 2,
            "pattern_strength": decreasing_pattern / len(sorted_txs) if sorted_txs else 0
        }

    def _detect_consolidation_patterns(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect fund consolidation patterns."""
        # Look for addresses that receive from multiple sources
        consolidation_addresses = {}

        for tx in report:
            if tx.to_address not in consolidation_addresses:
                consolidation_addresses[tx.to_address] = {
                    "sources": set(),
                    "total_amount": 0,
                    "transaction_count": 0
                }

            consolidation_addresses[tx.to_address]["sources"].add(tx.from_address)
            consolidation_addresses[tx.to_address]["total_amount"] += tx.amount_btc
            consolidation_addresses[tx.to_address]["transaction_count"] += 1

        # Find addresses with significant consolidation
        significant_consolidation = {
            addr: data for addr, data in consolidation_addresses.items()
            if len(data["sources"]) > 2 and data["total_amount"] > 0.1
        }

        return {
            "detected": len(significant_consolidation) > 0,
            "consolidation_addresses": {
                addr: {
                    "source_count": len(data["sources"]),
                    "total_amount": data["total_amount"],
                    "transaction_count": data["transaction_count"]
                }
                for addr, data in significant_consolidation.items()
            },
            "severity": 1
        }

    def _detect_privacy_coin_interactions(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect potential privacy coin interactions (placeholder for future enhancement)."""
        # This would require cross-chain analysis capabilities
        # For now, return a placeholder structure
        return {
            "detected": False,
            "severity": 3,
            "note": "Cross-chain analysis not yet implemented"
        }

    def _classify_suspicion_level(self, score: int) -> str:
        """Classify overall suspicion level based on score."""
        if score >= 8:
            return "VERY_HIGH"
        elif score >= 6:
            return "HIGH"
        elif score >= 4:
            return "MEDIUM"
        elif score >= 2:
            return "LOW"
        else:
            return "MINIMAL"

    def trace_stolen_funds(self, initial_txid: str, target_address: str, max_depth: Optional[int] = None) -> Tuple[nx.DiGraph, List[TransactionInfo]]:
        """
        Traces the flow of funds from a starting transaction and address.

        Args:
            initial_txid: The transaction ID to start tracing from
            target_address: The address that received the initial funds
            max_depth: Maximum depth to trace (uses config default if None)

        Returns:
            Tuple of (graph, report) containing the transaction flow
        """
        if max_depth is None:
            max_depth = self.config.max_depth

        if not self.validate_transaction_id(initial_txid):
            logger.error(f"Invalid initial transaction ID: {initial_txid}")
            return nx.DiGraph(), []

        if not self.validate_bitcoin_address(target_address):
            logger.error(f"Invalid target address: {target_address}")
            return nx.DiGraph(), []

        logger.info(f"🕵️  Starting investigation from TXID: {initial_txid}")
        logger.info(f"🎯 Tracing funds sent to address: {target_address}")

        # Log investigation start
        self._log_audit_event("investigation_started", {
            "initial_txid": initial_txid,
            "target_address": target_address,
            "max_depth": max_depth
        })

        graph = nx.DiGraph()
        queue = deque([(initial_txid, target_address, 0)])  # (txid, address, depth)
        visited_txs: Set[str] = {initial_txid}
        visited_addresses: Set[str] = set()
        report: List[TransactionInfo] = []

        # Add the initial target address to the graph
        graph.add_node(target_address,
                      label=f"Initial Address:\n{target_address[:8]}...",
                      address_type="initial",
                      first_seen=datetime.now().isoformat())

        # Initialize progress tracking
        processed_transactions = 0
        progress_bar = tqdm(desc="Tracing transactions", unit="tx", leave=True)

        while queue:
            current_txid, source_address, depth = queue.popleft()

            if depth >= max_depth:
                logger.info(f"Reached maximum depth {max_depth} for address {source_address[:8]}...")
                continue

            # Update progress
            processed_transactions += 1
            progress_bar.update(1)
            progress_bar.set_description(f"Processing depth {depth} - {len(report)} transactions found")

            logger.info(f"Processing transaction {current_txid} at depth {depth}")
            tx_data = self.get_transaction_data(current_txid)
            if not tx_data:
                continue

            # Find outputs that go TO the source address (money flowing into it)
            source_outputs = []
            for i, vout in enumerate(tx_data['vout']):
                output_address = vout.get('scriptpubkey_address')
                if output_address == source_address:
                    source_outputs.append((i, vout))

            # For each output going to our source address, check if it gets spent
            for vout_index, vout in source_outputs:
                outspend_data = self.get_outspend_data(current_txid, vout_index)
                if not outspend_data or not outspend_data.get('spent'):
                    continue

                spending_txid = outspend_data['txid']
                if spending_txid in visited_txs:
                    continue

                visited_txs.add(spending_txid)

                # Get the spending transaction details
                spending_tx_data = self.get_transaction_data(spending_txid)
                if not spending_tx_data:
                    continue

                # Analyze where the funds went in the spending transaction
                for next_vout in spending_tx_data['vout']:
                    new_address = next_vout.get('scriptpubkey_address')
                    if not new_address or new_address == source_address:
                        continue

                    amount_btc = next_vout['value'] / 100_000_000

                    # Add to graph with enhanced metadata
                    if new_address not in visited_addresses:
                        graph.add_node(new_address,
                                     label=f"Address:\n{new_address[:8]}...\n{amount_btc:.8f} BTC",
                                     address_type="traced",
                                     first_seen=datetime.now().isoformat(),
                                     total_received=amount_btc)
                        visited_addresses.add(new_address)

                    graph.add_edge(source_address, new_address,
                                 label=f"{amount_btc:.8f} BTC",
                                 amount=amount_btc,
                                 txid=spending_txid,
                                 timestamp=datetime.now().isoformat())

                    # Create detailed report entry using dataclass
                    transaction_info = TransactionInfo(
                        txid=spending_txid,
                        from_address=source_address,
                        to_address=new_address,
                        amount_btc=amount_btc,
                        depth=depth + 1,
                        timestamp=datetime.now().isoformat(),
                        block_height=spending_tx_data.get('status', {}).get('block_height'),
                        confirmations=spending_tx_data.get('status', {}).get('confirmed', False),
                        investigation_id=self.investigation_id
                    )
                    report.append(transaction_info)

                    # Create evidence item for this transaction
                    self._create_evidence_item(
                        evidence_type="transaction",
                        description=f"Transaction {spending_txid}: {amount_btc:.8f} BTC from {source_address[:8]}... to {new_address[:8]}...",
                        data={
                            "transaction_info": asdict(transaction_info),
                            "raw_transaction_data": spending_tx_data,
                            "discovery_context": {
                                "parent_txid": current_txid,
                                "output_index": vout_index,
                                "discovery_depth": depth
                            }
                        }
                    )

                    # Continue tracing from this new address
                    queue.append((spending_txid, new_address, depth + 1))

        # Log investigation completion
        self._log_audit_event("investigation_completed", {
            "transactions_found": len(report),
            "addresses_discovered": len(graph.nodes),
            "total_amount_traced": sum(tx.amount_btc for tx in report) if report else 0
        })

        # Create summary evidence item
        if report:
            self._create_evidence_item(
                evidence_type="investigation_summary",
                description=f"Investigation summary: {len(report)} transactions traced",
                data={
                    "transaction_count": len(report),
                    "address_count": len(graph.nodes),
                    "total_amount": sum(tx.amount_btc for tx in report),
                    "max_depth_reached": max(tx.depth for tx in report),
                    "investigation_parameters": {
                        "initial_txid": initial_txid,
                        "target_address": target_address,
                        "max_depth": max_depth
                    }
                }
            )

        # Close progress bar
        progress_bar.close()

        logger.info(f"Investigation complete. Found {len(report)} transactions across {len(graph.nodes)} addresses")
        return graph, report

    def comprehensive_investigation(self, initial_txid: str, target_address: str, max_depth: Optional[int] = None) -> Dict[str, Any]:
        """
        Perform a comprehensive forensic investigation including transaction tracing and analysis.

        Returns:
            Dictionary containing all investigation results including graph, report, patterns, and risk analysis
        """
        logger.info("Starting comprehensive forensic investigation")

        # Perform basic transaction tracing
        graph, report = self.trace_stolen_funds(initial_txid, target_address, max_depth)

        # Perform advanced analysis if we have data
        analysis_results = {}
        if report:
            logger.info("Performing advanced pattern analysis")

            # Perform analysis
            transaction_patterns = self.analyze_transaction_patterns(report)
            suspicious_activity = self.detect_suspicious_activity(report)
            risk_assessment = self._generate_risk_assessment(report)

            analysis_results = {
                "transaction_patterns": transaction_patterns,
                "suspicious_activity": suspicious_activity,
                "risk_assessment": risk_assessment
            }

            # Create evidence items for analysis results
            self._create_evidence_item(
                evidence_type="pattern_analysis",
                description="Transaction pattern analysis results",
                data=transaction_patterns
            )

            self._create_evidence_item(
                evidence_type="suspicious_activity_analysis",
                description="Suspicious activity detection results",
                data=suspicious_activity
            )

            self._create_evidence_item(
                evidence_type="risk_assessment",
                description="Comprehensive risk assessment",
                data=risk_assessment
            )

        # Compile comprehensive results
        investigation_results = {
            "investigation_id": self.investigation_id,
            "timestamp": datetime.now().isoformat(),
            "input_parameters": {
                "initial_txid": initial_txid,
                "target_address": target_address,
                "max_depth": max_depth or self.config.max_depth
            },
            "basic_results": {
                "transaction_count": len(report),
                "address_count": len(graph.nodes),
                "total_amount": sum(tx.amount_btc for tx in report) if report else 0
            },
            "graph": graph,
            "detailed_transactions": report,
            "advanced_analysis": analysis_results,
            "investigation_summary": self._generate_investigation_summary(report, analysis_results)
        }

        logger.info("Comprehensive investigation completed")
        return investigation_results

    def _generate_risk_assessment(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Generate a comprehensive risk assessment."""
        if not report:
            return {"status": "no_data"}

        # Get individual risk components
        risk_indicators = self._calculate_risk_indicators(report)
        suspicious_activity = self.detect_suspicious_activity(report)

        # Calculate composite risk score
        base_risk = risk_indicators["risk_score"]
        suspicion_bonus = suspicious_activity["overall_suspicion_score"]
        composite_score = base_risk + suspicion_bonus

        # Determine final risk level
        if composite_score >= 12:
            final_risk_level = "CRITICAL"
        elif composite_score >= 9:
            final_risk_level = "HIGH"
        elif composite_score >= 6:
            final_risk_level = "MEDIUM"
        elif composite_score >= 3:
            final_risk_level = "LOW"
        else:
            final_risk_level = "MINIMAL"

        return {
            "composite_risk_score": composite_score,
            "final_risk_level": final_risk_level,
            "base_risk_score": base_risk,
            "suspicion_score": suspicion_bonus,
            "risk_factors": risk_indicators["risk_factors"],
            "suspicious_activities": [
                activity for activity, details in suspicious_activity.items()
                if isinstance(details, dict) and details.get("detected", False)
            ],
            "recommendations": self._generate_risk_recommendations(final_risk_level, suspicious_activity)
        }

    def _generate_investigation_summary(self, report: List[TransactionInfo], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a high-level investigation summary."""
        if not report:
            return {
                "status": "no_transactions_found",
                "message": "No transactions were traced from the initial parameters"
            }

        # Extract key findings
        total_amount = sum(tx.amount_btc for tx in report)
        unique_addresses = len(set(tx.to_address for tx in report))
        max_depth = max(tx.depth for tx in report)

        # Get risk level
        risk_level = "UNKNOWN"
        if "risk_assessment" in analysis:
            risk_level = analysis["risk_assessment"].get("final_risk_level", "UNKNOWN")

        # Identify key concerns
        key_concerns = []
        if "suspicious_activity" in analysis:
            suspicious = analysis["suspicious_activity"]
            for activity, details in suspicious.items():
                if isinstance(details, dict) and details.get("detected", False):
                    key_concerns.append(activity)

        return {
            "status": "investigation_completed",
            "key_metrics": {
                "total_transactions": len(report),
                "total_amount_btc": total_amount,
                "unique_addresses": unique_addresses,
                "maximum_depth": max_depth,
                "risk_level": risk_level
            },
            "key_concerns": key_concerns,
            "investigation_quality": self._assess_investigation_quality(report),
            "next_steps": self._suggest_next_steps(report, analysis)
        }

    def _generate_risk_recommendations(self, risk_level: str, suspicious_activity: Dict[str, Any]) -> List[str]:
        """Generate risk-based recommendations."""
        recommendations = []

        if risk_level in ["CRITICAL", "HIGH"]:
            recommendations.append("Immediate escalation to law enforcement recommended")
            recommendations.append("Consider freezing related accounts if possible")
            recommendations.append("Implement enhanced monitoring for related addresses")

        if risk_level in ["HIGH", "MEDIUM"]:
            recommendations.append("Enhanced due diligence required")
            recommendations.append("Consider filing suspicious activity report")

        # Activity-specific recommendations
        if suspicious_activity.get("mixing_services", {}).get("detected", False):
            recommendations.append("Potential mixing service usage detected - investigate further")

        if suspicious_activity.get("exchange_deposits", {}).get("detected", False):
            recommendations.append("Potential exchange deposits identified - contact exchanges for cooperation")

        return recommendations

    def _assess_investigation_quality(self, report: List[TransactionInfo]) -> Dict[str, Any]:
        """Assess the quality and completeness of the investigation."""
        quality_score = 0
        quality_factors = []

        if len(report) > 5:
            quality_score += 2
            quality_factors.append("sufficient_transaction_volume")

        # Check depth coverage
        max_depth = max(tx.depth for tx in report) if report else 0
        if max_depth >= 3:
            quality_score += 2
            quality_factors.append("good_depth_coverage")

        # Check for confirmed transactions
        confirmed_txs = sum(1 for tx in report if tx.confirmations)
        if confirmed_txs > len(report) * 0.8:
            quality_score += 1
            quality_factors.append("high_confirmation_rate")

        quality_level = "HIGH" if quality_score >= 4 else "MEDIUM" if quality_score >= 2 else "LOW"

        return {
            "quality_score": quality_score,
            "quality_level": quality_level,
            "quality_factors": quality_factors,
            "completeness_percentage": min(100, (quality_score / 5) * 100)
        }

    def _suggest_next_steps(self, report: List[TransactionInfo], analysis: Dict[str, Any]) -> List[str]:
        """Suggest next steps based on investigation results."""
        next_steps = []

        if not report:
            next_steps.append("Verify input parameters and try again")
            next_steps.append("Check if funds have been moved recently")
            return next_steps

        # Get the latest addresses for further investigation
        latest_addresses = [tx.to_address for tx in report if tx.depth == max(tx.depth for tx in report)]
        if latest_addresses:
            next_steps.append(f"Monitor latest addresses for new activity: {', '.join(latest_addresses[:3])}")

        # Risk-based suggestions
        risk_level = analysis.get("risk_assessment", {}).get("final_risk_level", "UNKNOWN")
        if risk_level in ["CRITICAL", "HIGH"]:
            next_steps.append("Contact law enforcement immediately")
            next_steps.append("Prepare detailed evidence package")

        next_steps.append("Set up monitoring alerts for identified addresses")
        next_steps.append("Consider expanding investigation depth if needed")

        return next_steps

    def generate_interactive_visualization(self, graph: nx.DiGraph, report: List[TransactionInfo], save_html: bool = True) -> None:
        """Creates an enhanced interactive Plotly graph of the transaction flow."""
        if not graph.nodes:
            logger.warning("No data available to generate a graph.")
            print("No data available to generate a graph.")
            return

        try:
            # Use hierarchical layout for better visualization
            pos = nx.spring_layout(graph, k=3, iterations=50)

            # Create edges with enhanced styling
            edge_x, edge_y, edge_info = [], [], []
            for edge in graph.edges(data=True):
                x0, y0 = pos[edge[0]]
                x1, y1 = pos[edge[1]]
                edge_x.extend([x0, x1, None])
                edge_y.extend([y0, y1, None])

                # Get edge data for hover info
                edge_data = edge[2]
                amount = edge_data.get('amount', 0)
                txid = edge_data.get('txid', 'Unknown')
                edge_info.append(f"Amount: {amount:.8f} BTC<br>TXID: {txid}")

            edge_trace = go.Scatter(
                x=edge_x, y=edge_y,
                line=dict(width=2, color='#888'),
                hoverinfo='none',
                mode='lines',
                name='Transactions'
            )

            # Create nodes with enhanced information
            node_x, node_y, node_text, node_colors, node_sizes = [], [], [], [], []
            for node in graph.nodes(data=True):
                x, y = pos[node[0]]
                node_x.append(x)
                node_y.append(y)

                node_data = node[1]
                address = node[0]
                address_type = node_data.get('address_type', 'unknown')

                # Enhanced hover text
                hover_text = f"Address: {address}<br>"
                hover_text += f"Type: {address_type}<br>"
                hover_text += f"First seen: {node_data.get('first_seen', 'Unknown')}<br>"
                hover_text += f"Explorer: https://mempool.space/address/{address}"

                node_text.append(hover_text)

                # Color coding based on address type
                if address_type == 'initial':
                    node_colors.append('red')
                    node_sizes.append(20)
                else:
                    node_colors.append('blue')
                    node_sizes.append(15)

            node_trace = go.Scatter(
                x=node_x, y=node_y,
                mode='markers',
                hoverinfo='text',
                text=node_text,
                marker=dict(
                    size=node_sizes,
                    color=node_colors,
                    line=dict(width=2, color='white'),
                    opacity=0.8
                ),
                name='Addresses'
            )

            # Create the figure with enhanced layout
            fig = go.Figure(
                data=[edge_trace, node_trace],
                layout=go.Layout(
                    title=dict(
                        text='Bitcoin Transaction Flow Investigation',
                        x=0.5,
                        font=dict(size=20)
                    ),
                    showlegend=True,
                    hovermode='closest',
                    margin=dict(b=20, l=5, r=5, t=40),
                    annotations=[
                        dict(
                            text=f"Investigation completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>"
                                 f"Nodes: {len(graph.nodes)} | Transactions: {len(report)}",
                            showarrow=False,
                            xref="paper", yref="paper",
                            x=0.005, y=-0.002,
                            xanchor='left', yanchor='bottom',
                            font=dict(size=12, color='gray')
                        )
                    ],
                    xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    plot_bgcolor='white'
                )
            )

            # Save HTML file if requested
            if save_html and self.config.save_visualizations:
                output_path = Path(self.config.output_directory)
                filename = output_path / f"investigation_{self.investigation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                fig.write_html(str(filename))
                logger.info(f"Visualization saved as {filename}")
                print(f"📊 Visualization saved as {filename}")

            fig.show()

        except Exception as e:
            logger.error(f"Error generating visualization: {e}")
            print(f"Error generating visualization: {e}")

    def print_report(self, report: List[TransactionInfo], save_to_file: bool = True) -> None:
        """Prints and optionally saves a formatted report of the transaction trail."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("                      FORENSIC INVESTIGATION REPORT")
        report_lines.append(f"                        Generated: {timestamp}")
        report_lines.append(f"                     Investigation ID: {self.investigation_id}")
        report_lines.append("=" * 80)
        report_lines.append("")

        if not report:
            report_lines.append("No subsequent transactions were traced from the initial address.")
            report_lines.append("")
        else:
            # Summary statistics
            total_amount = sum(entry.amount_btc for entry in report)
            unique_addresses = len(set(entry.to_address for entry in report))

            report_lines.append("📊 INVESTIGATION SUMMARY")
            report_lines.append("-" * 40)
            report_lines.append(f"Total transactions traced: {len(report)}")
            report_lines.append(f"Total amount moved: {total_amount:.8f} BTC")
            report_lines.append(f"Unique destination addresses: {unique_addresses}")
            report_lines.append("")

            # Detailed transaction list
            report_lines.append("📋 DETAILED TRANSACTION TRAIL")
            report_lines.append("-" * 40)

            for i, entry in enumerate(report, 1):
                report_lines.append(f"Transaction #{i} (Depth: {entry.depth})")
                report_lines.append(f"   ➡️  From: {entry.from_address}")
                report_lines.append(f"   🎯  To:   {entry.to_address}")
                report_lines.append(f"   💰  Amount: {entry.amount_btc:.8f} BTC")
                report_lines.append(f"   🔗  TXID: {entry.txid}")

                if entry.block_height:
                    report_lines.append(f"   📦  Block: {entry.block_height}")

                report_lines.append(f"   ✅  Confirmed: {entry.confirmations}")
                report_lines.append(f"   🕐  Timestamp: {entry.timestamp}")
                report_lines.append("")

        report_lines.append("=" * 80)
        report_lines.append("                           END OF REPORT")
        report_lines.append("=" * 80)

        # Print to console
        for line in report_lines:
            print(line)

        # Save to file if requested
        if save_to_file and self.config.save_reports:
            output_path = Path(self.config.output_directory)
            filename = output_path / f"investigation_report_{self.investigation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(report_lines))
                logger.info(f"Report saved to {filename}")
                print(f"\n📄 Report saved to {filename}")
            except Exception as e:
                logger.error(f"Error saving report to file: {e}")
                print(f"Error saving report to file: {e}")

def print_banner():
    """Print the application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 Bitcoin Forensic Investigation Tool v2.0                ║
║                          Professional Cryptocurrency Forensics                ║
╚══════════════════════════════════════════════════════════════════════════════╝

🎯 FEATURES:
   • Advanced transaction tracing and analysis
   • Suspicious activity detection and risk scoring
   • Professional evidence collection and chain of custody
   • Interactive visualizations and comprehensive reporting
   • Audit trail generation for legal proceedings

⚠️  LEGAL NOTICE:
   This tool is for authorized investigations only. Ensure you have proper
   legal authorization before investigating any Bitcoin addresses or transactions.

═══════════════════════════════════════════════════════════════════════════════
"""
    print(banner)

def print_help():
    """Print comprehensive help information."""
    help_text = """
🔍 BITCOIN FORENSIC INVESTIGATION TOOL - HELP GUIDE

OVERVIEW:
This tool traces Bitcoin transactions from an initial transaction to help
investigate fund flows, detect suspicious patterns, and generate evidence
packages suitable for legal proceedings.

BASIC USAGE:
1. Run the tool: python crypto_investigator.py
2. Enter the initial transaction ID (TXID) where funds were sent
3. Enter the address that received the funds
4. Specify maximum tracing depth (1-10, default: 5)
5. Review results and generated files

INPUT REQUIREMENTS:
• Transaction ID: 64-character hexadecimal string
• Bitcoin Address: Valid Bitcoin address (Legacy, SegWit, or Taproot)
• Max Depth: Number between 1-10 (higher = more comprehensive but slower)

OUTPUT FILES:
• investigation_report_[ID]_[timestamp].txt - Detailed text report
• investigation_[ID]_[timestamp].html - Interactive visualization
• comprehensive_investigation_[ID].json - Complete investigation data
• evidence_package_[ID].json - Legal evidence package
• audit_trail_[ID].json - Chain of custody documentation

ANALYSIS FEATURES:
• Transaction Pattern Analysis: Timing, amounts, address reuse
• Suspicious Activity Detection: Mixing services, exchanges, peel chains
• Risk Assessment: Composite risk scoring with recommendations
• Evidence Collection: Cryptographic hashing and chain of custody

RISK LEVELS:
• MINIMAL: Low-risk, standard transaction patterns
• LOW: Some concerning patterns but likely legitimate
• MEDIUM: Multiple risk factors, enhanced monitoring recommended
• HIGH: Significant suspicious activity, investigation recommended
• CRITICAL: Severe risk indicators, immediate action required

EXAMPLES:
• Small investigation: Max depth 3-5, faster results
• Comprehensive investigation: Max depth 7-10, thorough analysis
• Quick check: Max depth 1-2, immediate connections only

TROUBLESHOOTING:
• "No transactions found": Check TXID/address combination
• "Invalid format": Verify TXID is 64 hex chars, address is valid Bitcoin
• "Rate limited": Tool automatically handles API limits with delays
• "Network errors": Check internet connection, tool will retry automatically

For technical support or questions about legal compliance, consult with
qualified legal and technical professionals.
"""
    print(help_text)

def create_argument_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Bitcoin Forensic Investigation Tool v2.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python crypto_investigator.py --help
  python crypto_investigator.py --interactive
  python crypto_investigator.py --txid abc123... --address 1A1zP1eP... --depth 5
        """
    )

    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Run in interactive mode (default)'
    )

    parser.add_argument(
        '--txid', '-t',
        type=str,
        help='Initial transaction ID to trace from'
    )

    parser.add_argument(
        '--address', '-a',
        type=str,
        help='Target address that received the funds'
    )

    parser.add_argument(
        '--depth', '-d',
        type=int,
        default=5,
        choices=range(1, 11),
        help='Maximum tracing depth (1-10, default: 5)'
    )

    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='investigation_results',
        help='Output directory for results (default: investigation_results)'
    )

    parser.add_argument(
        '--no-visualization',
        action='store_true',
        help='Skip generating interactive visualization'
    )

    parser.add_argument(
        '--no-evidence',
        action='store_true',
        help='Skip generating evidence package'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--help-guide',
        action='store_true',
        help='Show comprehensive help guide'
    )

    return parser

def run_non_interactive(args):
    """Run investigation in non-interactive mode."""
    if not args.txid or not args.address:
        print("❌ Error: Both --txid and --address are required for non-interactive mode")
        return False

    # Create configuration
    config = InvestigationConfig(
        output_directory=args.output_dir,
        max_depth=args.depth,
        save_visualizations=not args.no_visualization,
        save_reports=True
    )

    # Initialize investigator
    investigator = BitcoinForensicsInvestigator(config)

    print(f"🚀 Starting non-interactive investigation...")
    print(f"   TXID: {args.txid}")
    print(f"   Address: {args.address}")
    print(f"   Max Depth: {args.depth}")
    print("-" * 50)

    try:
        # Perform investigation
        investigation_results = investigator.comprehensive_investigation(
            args.txid, args.address, args.depth
        )

        report = investigation_results["detailed_transactions"]
        graph = investigation_results["graph"]

        if not report:
            print("\n⚠️  No transactions found.")
            return False

        print(f"\n✅ Investigation completed!")
        print(f"   Found {len(report)} transactions across {len(graph.nodes)} addresses")

        # Generate outputs
        investigator.print_report(report, save_to_file=True)
        if not args.no_visualization:
            investigator.generate_interactive_visualization(graph, report, save_html=True)

        if not args.no_evidence:
            evidence_file = investigator.export_evidence_package()
            print(f"📄 Evidence package: {evidence_file}")

        return True

    except Exception as e:
        logger.error(f"Investigation failed: {e}")
        print(f"❌ Investigation failed: {e}")
        return False

def main():
    """Main function to drive the investigation tool."""
    # Parse command line arguments
    parser = create_argument_parser()
    args = parser.parse_args()

    # Show help guide if requested
    if args.help_guide:
        print_help()
        return

    # Set up logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Print banner
    print_banner()

    # Run in non-interactive mode if arguments provided
    if args.txid and args.address:
        success = run_non_interactive(args)
        sys.exit(0 if success else 1)

    # Interactive mode
    print("🎯 INTERACTIVE MODE")
    print("Type 'help' for detailed guidance or 'quit' to exit")
    print("=" * 50)

    try:
        # Initialize the investigator
        config = InvestigationConfig(
            output_directory=getattr(args, 'output_dir', 'investigation_results'),
            save_visualizations=not getattr(args, 'no_visualization', False),
            save_reports=True
        )
        investigator = BitcoinForensicsInvestigator(config)

        # Get initial transaction ID
        while True:
            initial_txid = input("\n📝 Enter the initial TXID to start tracing from (or 'help'/'quit'): ").strip()

            if initial_txid.lower() == 'quit':
                print("👋 Goodbye!")
                return
            elif initial_txid.lower() == 'help':
                print_help()
                continue
            elif not initial_txid:
                print("❌ Transaction ID cannot be empty. Please try again.")
                continue
            elif investigator.validate_transaction_id(initial_txid):
                break
            else:
                print("❌ Invalid transaction ID format. Please enter a valid 64-character hex string.")
                print("💡 Example: a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890")

        # Get target address
        while True:
            target_address = input("🎯 Enter the address that received the funds (or 'help'/'quit'): ").strip()

            if target_address.lower() == 'quit':
                print("👋 Goodbye!")
                return
            elif target_address.lower() == 'help':
                print_help()
                continue
            elif not target_address:
                print("❌ Address cannot be empty. Please try again.")
                continue
            elif investigator.validate_bitcoin_address(target_address):
                break
            else:
                print("❌ Invalid Bitcoin address format. Please try again.")
                print("💡 Examples:")
                print("   Legacy: **********************************")
                print("   SegWit: ******************************************")
                print("   Taproot: **************************************************************")

        # Get maximum depth
        while True:
            try:
                depth_input = input("🔢 Enter maximum tracing depth (1-10, default 5): ").strip()
                if not depth_input:
                    max_depth = 5
                    break
                max_depth = int(depth_input)
                if 1 <= max_depth <= 10:
                    break
                print("❌ Depth must be between 1 and 10.")
            except ValueError:
                print("❌ Please enter a valid number.")

        print(f"\n🚀 Starting investigation...")
        print(f"   Investigation ID: {investigator.investigation_id}")
        print(f"   Initial TXID: {initial_txid}")
        print(f"   Target Address: {target_address}")
        print(f"   Max Depth: {max_depth}")
        print("-" * 50)

        # Perform comprehensive investigation
        investigation_results = investigator.comprehensive_investigation(initial_txid, target_address, max_depth)

        report = investigation_results["detailed_transactions"]
        graph = investigation_results["graph"]

        if not report:
            print("\n⚠️  No transactions found. This could mean:")
            print("   - The funds haven't been moved yet")
            print("   - The transaction/address combination is incorrect")
            print("   - The funds were moved to non-standard outputs")
        else:
            print(f"\n✅ Investigation completed successfully!")
            print(f"   Found {len(report)} transactions across {len(graph.nodes)} addresses")

            # Display key findings
            summary = investigation_results["investigation_summary"]
            risk_assessment = investigation_results["advanced_analysis"].get("risk_assessment", {})

            print(f"\n🎯 KEY FINDINGS:")
            print(f"   Total Amount Traced: {summary['key_metrics']['total_amount_btc']:.8f} BTC")
            print(f"   Risk Level: {risk_assessment.get('final_risk_level', 'UNKNOWN')}")
            print(f"   Investigation Quality: {summary['investigation_quality']['quality_level']}")

            if summary["key_concerns"]:
                print(f"   ⚠️  Concerns: {', '.join(summary['key_concerns'])}")

        # Generate outputs
        print("\n📊 Generating comprehensive report and visualization...")
        investigator.print_report(report, save_to_file=True)
        investigator.generate_interactive_visualization(graph, report, save_html=True)

        # Export evidence package
        print("\n📦 Exporting evidence package...")
        try:
            evidence_file = investigator.export_evidence_package()
            print(f"📄 Evidence package exported: {evidence_file}")

            # Generate audit trail report
            audit_report = investigator.generate_audit_trail_report()
            audit_file = Path(investigator.config.output_directory) / f"audit_trail_{investigator.investigation_id}.json"
            with open(audit_file, 'w') as f:
                json.dump(audit_report, f, indent=2, default=str)
            print(f"📋 Audit trail saved: {audit_file}")

        except Exception as e:
            logger.error(f"Error exporting evidence package: {e}")
            print(f"❌ Error exporting evidence package: {e}")

        # Save comprehensive investigation results
        if investigation_results and investigator.config.save_reports:
            output_path = Path(investigator.config.output_directory)
            results_file = output_path / f"comprehensive_investigation_{investigator.investigation_id}.json"
            try:
                # Convert graph to serializable format
                serializable_results = investigation_results.copy()
                serializable_results["graph"] = {
                    "nodes": list(graph.nodes(data=True)),
                    "edges": list(graph.edges(data=True))
                }
                serializable_results["detailed_transactions"] = [asdict(tx) for tx in report]

                with open(results_file, 'w') as f:
                    json.dump(serializable_results, f, indent=2, default=str)
                print(f"📄 Comprehensive results saved to {results_file}")
            except Exception as e:
                logger.error(f"Error saving comprehensive results: {e}")

        # Final summary
        print("\n" + "="*80)
        print("                        🎉 INVESTIGATION COMPLETE!")
        print("="*80)

        if report:
            summary = investigation_results["investigation_summary"]
            risk_assessment = investigation_results["advanced_analysis"].get("risk_assessment", {})

            print(f"\n📊 FINAL SUMMARY:")
            print(f"   Investigation ID: {investigator.investigation_id}")
            print(f"   Transactions Found: {len(report)}")
            print(f"   Addresses Discovered: {len(graph.nodes)}")
            print(f"   Total Amount Traced: {summary['key_metrics']['total_amount_btc']:.8f} BTC")
            print(f"   Maximum Depth Reached: {summary['key_metrics']['maximum_depth']}")
            print(f"   Risk Level: {risk_assessment.get('final_risk_level', 'UNKNOWN')}")
            print(f"   Investigation Quality: {summary['investigation_quality']['quality_level']}")

            if summary["key_concerns"]:
                print(f"\n⚠️  KEY CONCERNS IDENTIFIED:")
                for concern in summary["key_concerns"]:
                    print(f"   • {concern.replace('_', ' ').title()}")

            # Show recommendations
            recommendations = risk_assessment.get("recommendations", [])
            if recommendations:
                print(f"\n💡 RECOMMENDATIONS:")
                for rec in recommendations[:3]:  # Show top 3 recommendations
                    print(f"   • {rec}")

            # Show next steps
            next_steps = summary.get("next_steps", [])
            if next_steps:
                print(f"\n🎯 SUGGESTED NEXT STEPS:")
                for step in next_steps[:3]:  # Show top 3 next steps
                    print(f"   • {step}")

        print(f"\n📁 OUTPUT FILES GENERATED:")
        print(f"   📄 Text Report: investigation_report_{investigator.investigation_id}_*.txt")
        print(f"   📊 Visualization: investigation_{investigator.investigation_id}_*.html")
        print(f"   📦 Evidence Package: evidence_package_{investigator.investigation_id}.json")
        print(f"   📋 Audit Trail: audit_trail_{investigator.investigation_id}.json")
        print(f"   📈 Complete Data: comprehensive_investigation_{investigator.investigation_id}.json")

        print(f"\n🎉 Investigation complete! All files saved to: {investigator.config.output_directory}")
        print("="*80)

    except KeyboardInterrupt:
        print("\n\n⚠️  Investigation interrupted by user.")
        logger.info("Investigation interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        print(f"\n❌ An unexpected error occurred: {e}")
        print("Please check the log file for more details.")

if __name__ == "__main__":
    main()
