import requests
import networkx as nx
import plotly.graph_objects as go
from collections import deque

# Using the public Blockstream API
API_BASE_URL = "https://blockstream.info/api/"

def get_transaction_data(txid):
    """Fetches detailed transaction data from the Blockstream API."""
    try:
        response = requests.get(f"{API_BASE_URL}tx/{txid}")
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching transaction {txid}: {e}")
        return None

def trace_stolen_funds(initial_txid, scammer_address):
    """
    Traces the flow of funds from a starting transaction and address,
    building a graph and a report.
    """
    print(f"🕵️  Starting investigation from TXID: {initial_txid}")
    print(f"🎯 Tracing funds sent to address: {scammer_address}\n")

    graph = nx.DiGraph()
    queue = deque([(initial_txid, scammer_address, 0)])  # (txid, address, depth)
    visited_txs = {initial_txid}
    report = []

    # Add the initial scammer address to the graph
    graph.add_node(scammer_address, label=f"Scammer Address:\n{scammer_address[:8]}...")

    while queue:
        current_txid, source_address, depth = queue.popleft()

        if depth >= 5: # Limit the depth to keep the graph manageable
            continue

        tx_data = get_transaction_data(current_txid)
        if not tx_data:
            continue

        # Find outputs from the current transaction that are spent
        for i, vout in enumerate(tx_data['vout']):
            output_address = vout.get('scriptpubkey_address')
            if output_address == source_address:

                # Check if this output was spent in a subsequent transaction
                outspend_res = requests.get(f"{API_BASE_URL}tx/{current_txid}/outspend/{i}")
                if outspend_res.status_code == 200:
                    outspend_data = outspend_res.json()
                    if outspend_data.get('spent'):
                        spending_txid = outspend_data['txid']
                        if spending_txid not in visited_txs:
                            visited_txs.add(spending_txid)

                            # Get details of the spending transaction
                            next_tx_data = get_transaction_data(spending_txid)
                            if not next_tx_data:
                                continue

                            for next_vout in next_tx_data['vout']:
                                new_address = next_vout.get('scriptpubkey_address')
                                if new_address:
                                    amount_btc = next_vout['value'] / 100_000_000

                                    # Add to graph and report
                                    graph.add_node(new_address, label=f"Address:\n{new_address[:8]}...\n{amount_btc} BTC")
                                    graph.add_edge(source_address, new_address, label=f"{amount_btc} BTC")

                                    report_entry = {
                                        "from": source_address,
                                        "to": new_address,
                                        "amount_btc": amount_btc,
                                        "transaction_id": spending_txid
                                    }
                                    report.append(report_entry)

                                    queue.append((spending_txid, new_address, depth + 1))
    return graph, report

def generate_interactive_visualization(graph, report):
    """Creates an interactive Plotly graph of the transaction flow."""
    if not graph.nodes:
        print("No data available to generate a graph.")
        return

    pos = nx.spring_layout(graph)
    edge_x, edge_y = [], []
    for edge in graph.edges():
        x0, y0 = pos[edge[0]]
        x1, y1 = pos[edge[1]]
        edge_x.extend([x0, x1, None])
        edge_y.extend([y0, y1, None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        line=dict(width=0.5, color='#888'),
        hoverinfo='none',
        mode='lines')

    node_x, node_y, node_text = [], [], []
    for node in graph.nodes():
        x, y = pos[node]
        node_x.append(x)
        node_y.append(y)
        node_text.append(f"{graph.nodes[node]['label']}<br>Explorer: mempool.space/address/{node}")

    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        hoverinfo='text',
        text=node_text,
        marker=dict(
            showscale=True,
            colorscale='YlGnBu',
            size=10,
            colorbar=dict(
                thickness=15,
                title='Node Connections',
                xanchor='left',
                titleside='right'
            )
        )
    )

    fig = go.Figure(data=[edge_trace, node_trace],
                 layout=go.Layout(
                    title='<br>Bitcoin Transaction Flow Investigation',
                    showlegend=False,
                    hovermode='closest',
                    margin=dict(b=20,l=5,r=5,t=40),
                    xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                    yaxis=dict(showgrid=False, zeroline=False, showticklabels=False))
                    )
    fig.show()

def print_report(report):
    """Prints a formatted report of the transaction trail."""
    print("\n" + "="*80)
    print("                      FORENSIC INVESTIGATION REPORT")
    print("="*80 + "\n")

    if not report:
        print("No subsequent transactions were traced from the initial address.")
        return

    for entry in report:
        print(f"➡️  **Funds Moved**")
        print(f"   - **From**: {entry['from']}")
        print(f"   - **To**:     {entry['to']}")
        print(f"   - **Amount**: {entry['amount_btc']} BTC")
        print(f"   - **Transaction ID**: {entry['transaction_id']}\n")

    print("\n--- END OF REPORT ---\n")

def main():
    """Main function to drive the investigation tool."""
    print("Welcome to the Bitcoin Forensic Investigation Tool")
    initial_txid = input("Enter the initial TXID where you sent the funds: ").strip()
    scammer_address = input("Enter the scammer's address that received the funds: ").strip()

    graph, report = trace_stolen_funds(initial_txid, scammer_address)

    print_report(report)
    generate_interactive_visualization(graph, report)

if __name__ == "__main__":
    main()
