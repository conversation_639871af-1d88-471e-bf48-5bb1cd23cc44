{"investigation_metadata": {"investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "export_timestamp": "2025-07-13T17:31:16.896255", "investigator_version": "2.0.0", "export_format": "json"}, "audit_trail": {"investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "report_timestamp": "2025-07-13T17:31:16.896256", "total_audit_events": 3, "total_evidence_items": 1, "audit_events": [{"timestamp": "2025-07-13T17:31:16.895614", "investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "action": "investigation_initialized", "details": {"investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "config": {"api_base_url": "https://blockstream.info/api/", "rate_limit_delay": 0.1, "max_retries": 3, "request_timeout": 30, "max_depth": 5, "save_reports": true, "save_visualizations": true, "output_directory": "investigation_results"}}, "user_id": "system", "ip_address": "localhost"}, {"timestamp": "2025-07-13T17:31:16.896100", "investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "action": "test_action", "details": {"test": "data"}, "user_id": "system", "ip_address": "localhost"}, {"timestamp": "2025-07-13T17:31:16.896164", "investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "action": "evidence_created", "details": {"evidence_id": "534df794-df36-4d98-b7ee-fecc2570476d", "evidence_type": "test_evidence", "description": "Test evidence item", "hash": "d04b4bd81bc4a691dbe266f8a918aa743720b93e0ce1ae801b06092572cbcb2e"}, "user_id": "system", "ip_address": "localhost"}], "evidence_summary": [{"evidence_id": "534df794-df36-4d98-b7ee-fecc2570476d", "type": "test_evidence", "description": "Test evidence item", "timestamp": "2025-07-13T17:31:16.896160", "hash": "d04b4bd81bc4a691dbe266f8a918aa743720b93e0ce1ae801b06092572cbcb2e", "custody_events": 1}]}, "evidence_items": [{"evidence_id": "534df794-df36-4d98-b7ee-fecc2570476d", "investigation_id": "2a9ddcbb-b61b-4734-ba91-88c17d4c7b33", "evidence_type": "test_evidence", "description": "Test evidence item", "data": {"sample": "data", "value": 123}, "timestamp": "2025-07-13T17:31:16.896160", "hash_value": "d04b4bd81bc4a691dbe266f8a918aa743720b93e0ce1ae801b06092572cbcb2e", "chain_of_custody": [{"timestamp": "2025-07-13T17:31:16.896159", "action": "evidence_created", "user_id": "system", "description": "Evidence item created: Test evidence item"}]}], "integrity_verification": {"total_evidence_items": 1, "package_hash": "23984b3de265fd361e98c35c77ccdc41bdf44f7f873f607a2edca19998d78c1c"}}